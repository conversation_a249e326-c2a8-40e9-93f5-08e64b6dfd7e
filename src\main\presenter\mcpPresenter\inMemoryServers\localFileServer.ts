import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js'
import fs from 'fs/promises'
import path from 'path'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js'
import { createHash } from 'crypto'
import { app } from 'electron'

// --- Zod Schemas for Tool Arguments ---

const UploadFileArgsSchema = z.object({
  filePath: z.string().describe('Path to the file to upload'),
  targetDirectory: z.string().optional().describe('Target directory for the uploaded file (optional, defaults to uploads folder)')
})

const UploadMultipleFilesArgsSchema = z.object({
  filePaths: z.array(z.string()).describe('Array of file paths to upload'),
  targetDirectory: z.string().optional().describe('Target directory for the uploaded files (optional, defaults to uploads folder)')
})

const ListUploadedFilesArgsSchema = z.object({
  directory: z.string().optional().describe('Directory to list files from (optional, defaults to uploads folder)')
})

const DeleteUploadedFileArgsSchema = z.object({
  fileName: z.string().describe('Name of the uploaded file to delete')
})

const GetFileInfoArgsSchema = z.object({
  fileName: z.string().describe('Name of the uploaded file to get info for')
})

// --- Local File Server Implementation ---

export class LocalFileServer {
  private server: Server
  private uploadsDirectory: string
  private baseUrl: string

  constructor(uploadsDirectory?: string, baseUrl?: string) {
    // 设置上传目录，默认为用户数据目录下的uploads文件夹
    this.uploadsDirectory = uploadsDirectory || path.join(app.getPath('userData'), 'uploads')
    
    // 设置基础URL，默认为本地文件协议
    this.baseUrl = baseUrl || 'file://'

    this.server = new Server(
      {
        name: 'local-file-server',
        version: '0.1.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    )
    this.setupRequestHandlers()
  }

  // 初始化方法，确保上传目录存在
  public async initialize(): Promise<void> {
    try {
      await fs.mkdir(this.uploadsDirectory, { recursive: true })
      console.log(`Local file server initialized. Upload directory: ${this.uploadsDirectory}`)
    } catch (error) {
      throw new Error(`Failed to initialize upload directory: ${error}`)
    }
  }

  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  // 生成文件的唯一名称以避免冲突
  private generateUniqueFileName(originalPath: string): string {
    const ext = path.extname(originalPath)
    const baseName = path.basename(originalPath, ext)
    const timestamp = Date.now()
    const randomSuffix = Math.random().toString(36).substring(2, 8)
    const hash = createHash('md5').update(originalPath + timestamp + randomSuffix).digest('hex').substring(0, 8)
    return `${baseName}_${timestamp}_${hash}${ext}`
  }

  // 上传单个文件
  private async uploadFile(filePath: string, targetDirectory?: string): Promise<string> {
    try {
      // 验证源文件是否存在
      const stats = await fs.stat(filePath)
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${filePath}`)
      }

      // 确定目标目录
      const targetDir = targetDirectory 
        ? path.join(this.uploadsDirectory, targetDirectory)
        : this.uploadsDirectory

      // 确保目标目录存在
      await fs.mkdir(targetDir, { recursive: true })

      // 生成唯一文件名
      const uniqueFileName = this.generateUniqueFileName(filePath)
      const targetPath = path.join(targetDir, uniqueFileName)

      // 复制文件
      await fs.copyFile(filePath, targetPath)

      // 生成文件URL
      const relativePath = path.relative(this.uploadsDirectory, targetPath)
      const fileUrl = this.baseUrl + path.posix.join(...relativePath.split(path.sep))

      console.log(`File uploaded successfully: ${filePath} -> ${targetPath}`)
      return fileUrl

    } catch (error) {
      throw new Error(`Failed to upload file ${filePath}: ${error}`)
    }
  }

  // 列出已上传的文件
  private async listUploadedFiles(directory?: string): Promise<Array<{name: string, size: number, modified: Date, url: string}>> {
    try {
      const targetDir = directory
        ? path.join(this.uploadsDirectory, directory)
        : this.uploadsDirectory

      const entries = await fs.readdir(targetDir, { withFileTypes: true })
      const files: Array<{name: string, size: number, modified: Date, url: string}> = []

      for (const entry of entries) {
        if (entry.isFile()) {
          const filePath = path.join(targetDir, entry.name)
          const stats = await fs.stat(filePath)
          const relativePath = path.relative(this.uploadsDirectory, filePath)
          const fileUrl = this.baseUrl + path.posix.join(...relativePath.split(path.sep))

          files.push({
            name: entry.name,
            size: stats.size,
            modified: stats.mtime,
            url: fileUrl
          })
        }
      }

      return files.sort((a, b) => b.modified.getTime() - a.modified.getTime())
    } catch (error) {
      throw new Error(`Failed to list uploaded files: ${error}`)
    }
  }

  // 删除已上传的文件
  private async deleteUploadedFile(fileName: string): Promise<void> {
    try {
      const filePath = path.join(this.uploadsDirectory, fileName)
      
      // 验证文件存在且在上传目录内
      const resolvedPath = path.resolve(filePath)
      const resolvedUploadsDir = path.resolve(this.uploadsDirectory)
      
      if (!resolvedPath.startsWith(resolvedUploadsDir)) {
        throw new Error('File is outside uploads directory')
      }

      await fs.unlink(filePath)
      console.log(`File deleted successfully: ${filePath}`)
    } catch (error) {
      throw new Error(`Failed to delete file ${fileName}: ${error}`)
    }
  }

  // 获取文件信息
  private async getFileInfo(fileName: string): Promise<{name: string, size: number, modified: Date, url: string, mimeType?: string}> {
    try {
      const filePath = path.join(this.uploadsDirectory, fileName)
      
      // 验证文件存在且在上传目录内
      const resolvedPath = path.resolve(filePath)
      const resolvedUploadsDir = path.resolve(this.uploadsDirectory)
      
      if (!resolvedPath.startsWith(resolvedUploadsDir)) {
        throw new Error('File is outside uploads directory')
      }

      const stats = await fs.stat(filePath)
      if (!stats.isFile()) {
        throw new Error('Path is not a file')
      }

      const relativePath = path.relative(this.uploadsDirectory, filePath)
      const fileUrl = this.baseUrl + path.posix.join(...relativePath.split(path.sep))

      // 简单的MIME类型检测
      const ext = path.extname(fileName).toLowerCase()
      const mimeTypes: Record<string, string> = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.pdf': 'application/pdf',
        '.txt': 'text/plain',
        '.json': 'application/json',
        '.xml': 'application/xml',
        '.zip': 'application/zip'
      }

      return {
        name: fileName,
        size: stats.size,
        modified: stats.mtime,
        url: fileUrl,
        mimeType: mimeTypes[ext] || 'application/octet-stream'
      }
    } catch (error) {
      throw new Error(`Failed to get file info for ${fileName}: ${error}`)
    }
  }

  private setupRequestHandlers(): void {
    // List Tools Handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'upload_file',
            description: 'Upload a file to the local file server and return its URL',
            inputSchema: zodToJsonSchema(UploadFileArgsSchema)
          },
          {
            name: 'upload_multiple_files',
            description: 'Upload multiple files to the local file server and return their URLs',
            inputSchema: zodToJsonSchema(UploadMultipleFilesArgsSchema)
          },
          {
            name: 'list_uploaded_files',
            description: 'List all uploaded files with their information',
            inputSchema: zodToJsonSchema(ListUploadedFilesArgsSchema)
          },
          {
            name: 'delete_uploaded_file',
            description: 'Delete an uploaded file from the server',
            inputSchema: zodToJsonSchema(DeleteUploadedFileArgsSchema)
          },
          {
            name: 'get_file_info',
            description: 'Get detailed information about an uploaded file',
            inputSchema: zodToJsonSchema(GetFileInfoArgsSchema)
          }
        ]
      }
    })

    // Call Tool Handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params

      switch (name) {
        case 'upload_file': {
          const parsed = UploadFileArgsSchema.safeParse(args)
          if (!parsed.success) {
            throw new Error(`Invalid arguments for ${name}: ${parsed.error}`)
          }

          const fileUrl = await this.uploadFile(parsed.data.filePath, parsed.data.targetDirectory)
          return {
            content: [{ 
              type: 'text', 
              text: `File uploaded successfully!\nOriginal: ${parsed.data.filePath}\nURL: ${fileUrl}` 
            }]
          }
        }

        case 'upload_multiple_files': {
          const parsed = UploadMultipleFilesArgsSchema.safeParse(args)
          if (!parsed.success) {
            throw new Error(`Invalid arguments for ${name}: ${parsed.error}`)
          }

          const results = await Promise.allSettled(
            parsed.data.filePaths.map(filePath => 
              this.uploadFile(filePath, parsed.data.targetDirectory)
            )
          )

          const successResults = results
            .map((result, index) => ({
              original: parsed.data.filePaths[index],
              result
            }))
            .map(({ original, result }) => {
              if (result.status === 'fulfilled') {
                return `✓ ${original} -> ${result.value}`
              } else {
                return `✗ ${original} -> Error: ${result.reason}`
              }
            })

          return {
            content: [{ 
              type: 'text', 
              text: `Upload Results:\n${successResults.join('\n')}` 
            }]
          }
        }

        case 'list_uploaded_files': {
          const parsed = ListUploadedFilesArgsSchema.safeParse(args)
          if (!parsed.success) {
            throw new Error(`Invalid arguments for ${name}: ${parsed.error}`)
          }

          const files = await this.listUploadedFiles(parsed.data.directory)
          
          if (files.length === 0) {
            return {
              content: [{ type: 'text', text: 'No uploaded files found.' }]
            }
          }

          const fileList = files.map(file => 
            `${file.name} (${(file.size / 1024).toFixed(2)} KB, ${file.modified.toISOString()}) - ${file.url}`
          ).join('\n')

          return {
            content: [{ 
              type: 'text', 
              text: `Uploaded Files (${files.length}):\n${fileList}` 
            }]
          }
        }

        case 'delete_uploaded_file': {
          const parsed = DeleteUploadedFileArgsSchema.safeParse(args)
          if (!parsed.success) {
            throw new Error(`Invalid arguments for ${name}: ${parsed.error}`)
          }

          await this.deleteUploadedFile(parsed.data.fileName)
          return {
            content: [{ 
              type: 'text', 
              text: `File deleted successfully: ${parsed.data.fileName}` 
            }]
          }
        }

        case 'get_file_info': {
          const parsed = GetFileInfoArgsSchema.safeParse(args)
          if (!parsed.success) {
            throw new Error(`Invalid arguments for ${name}: ${parsed.error}`)
          }

          const fileInfo = await this.getFileInfo(parsed.data.fileName)
          const infoText = [
            `Name: ${fileInfo.name}`,
            `Size: ${(fileInfo.size / 1024).toFixed(2)} KB`,
            `Modified: ${fileInfo.modified.toISOString()}`,
            `MIME Type: ${fileInfo.mimeType}`,
            `URL: ${fileInfo.url}`
          ].join('\n')

          return {
            content: [{ type: 'text', text: infoText }]
          }
        }

        default:
          throw new Error(`Unknown tool: ${name}`)
      }
    })
  }
}
