import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { LocalFileServer } from '../../../src/main/presenter/mcpPresenter/inMemoryServers/localFileServer'
import { InMemoryTransport } from '@modelcontextprotocol/sdk/inMemory.js'
import fs from 'fs/promises'
import path from 'path'
import os from 'os'

describe('LocalFileServer', () => {
  let server: LocalFileServer
  let tempDir: string
  let uploadsDir: string
  let testFile: string

  beforeEach(async () => {
    // 创建临时目录
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'localfileserver-test-'))
    uploadsDir = path.join(tempDir, 'uploads')
    
    // 创建测试文件
    testFile = path.join(tempDir, 'test.txt')
    await fs.writeFile(testFile, 'This is a test file content', 'utf-8')

    // 创建服务器实例
    server = new LocalFileServer(uploadsDir, 'file://')
    await server.initialize()

    // 不需要在测试中启动服务器，我们直接测试内部方法
  })

  afterEach(async () => {
    // 清理临时目录
    try {
      await fs.rm(tempDir, { recursive: true, force: true })
    } catch (error) {
      console.warn('Failed to clean up temp directory:', error)
    }
  })

  describe('initialization', () => {
    it('should create uploads directory', async () => {
      const stats = await fs.stat(uploadsDir)
      expect(stats.isDirectory()).toBe(true)
    })
  })

  describe('file upload', () => {
    it('should upload a single file successfully', async () => {
      // 这里我们需要模拟MCP客户端调用
      // 由于测试环境的限制，我们直接测试内部方法
      const uploadMethod = (server as any).uploadFile.bind(server)
      const fileUrl = await uploadMethod(testFile)
      
      expect(fileUrl).toContain('file://')
      expect(fileUrl).toContain('test')
      
      // 验证文件是否被复制到上传目录
      const uploadedFiles = await fs.readdir(uploadsDir)
      expect(uploadedFiles.length).toBe(1)
      expect(uploadedFiles[0]).toContain('test')
    })

    it('should generate unique file names for duplicate uploads', async () => {
      const uploadMethod = (server as any).uploadFile.bind(server)
      
      // 上传同一个文件两次
      const url1 = await uploadMethod(testFile)
      const url2 = await uploadMethod(testFile)
      
      expect(url1).not.toBe(url2)
      
      // 验证两个文件都存在
      const uploadedFiles = await fs.readdir(uploadsDir)
      expect(uploadedFiles.length).toBe(2)
    })

    it('should handle target directory parameter', async () => {
      const uploadMethod = (server as any).uploadFile.bind(server)
      const targetDir = 'documents'
      
      const fileUrl = await uploadMethod(testFile, targetDir)
      
      expect(fileUrl).toContain('documents')
      
      // 验证目标目录被创建
      const targetPath = path.join(uploadsDir, targetDir)
      const stats = await fs.stat(targetPath)
      expect(stats.isDirectory()).toBe(true)
      
      // 验证文件在目标目录中
      const files = await fs.readdir(targetPath)
      expect(files.length).toBe(1)
    })
  })

  describe('file listing', () => {
    it('should list uploaded files', async () => {
      const uploadMethod = (server as any).uploadFile.bind(server)
      const listMethod = (server as any).listUploadedFiles.bind(server)
      
      // 上传一个文件
      await uploadMethod(testFile)
      
      // 列出文件
      const files = await listMethod()
      
      expect(files.length).toBe(1)
      expect(files[0]).toHaveProperty('name')
      expect(files[0]).toHaveProperty('size')
      expect(files[0]).toHaveProperty('modified')
      expect(files[0]).toHaveProperty('url')
      expect(files[0].url).toContain('file://')
    })

    it('should return empty list when no files uploaded', async () => {
      const listMethod = (server as any).listUploadedFiles.bind(server)
      
      const files = await listMethod()
      expect(files.length).toBe(0)
    })
  })

  describe('file deletion', () => {
    it('should delete uploaded file', async () => {
      const uploadMethod = (server as any).uploadFile.bind(server)
      const deleteMethod = (server as any).deleteUploadedFile.bind(server)
      
      // 上传文件
      await uploadMethod(testFile)
      
      // 获取上传的文件名
      const uploadedFiles = await fs.readdir(uploadsDir)
      expect(uploadedFiles.length).toBe(1)
      
      // 删除文件
      await deleteMethod(uploadedFiles[0])
      
      // 验证文件被删除
      const remainingFiles = await fs.readdir(uploadsDir)
      expect(remainingFiles.length).toBe(0)
    })

    it('should throw error when trying to delete non-existent file', async () => {
      const deleteMethod = (server as any).deleteUploadedFile.bind(server)
      
      await expect(deleteMethod('non-existent.txt')).rejects.toThrow()
    })

    it('should prevent deletion of files outside uploads directory', async () => {
      const deleteMethod = (server as any).deleteUploadedFile.bind(server)
      
      // 尝试删除上传目录外的文件
      await expect(deleteMethod('../../../etc/passwd')).rejects.toThrow('outside uploads directory')
    })
  })

  describe('file info', () => {
    it('should get file information', async () => {
      const uploadMethod = (server as any).uploadFile.bind(server)
      const getInfoMethod = (server as any).getFileInfo.bind(server)
      
      // 上传文件
      await uploadMethod(testFile)
      
      // 获取上传的文件名
      const uploadedFiles = await fs.readdir(uploadsDir)
      const fileName = uploadedFiles[0]
      
      // 获取文件信息
      const info = await getInfoMethod(fileName)
      
      expect(info).toHaveProperty('name', fileName)
      expect(info).toHaveProperty('size')
      expect(info).toHaveProperty('modified')
      expect(info).toHaveProperty('url')
      expect(info).toHaveProperty('mimeType')
      expect(info.url).toContain('file://')
      expect(info.mimeType).toBe('text/plain')
    })

    it('should detect correct MIME types', async () => {
      const getInfoMethod = (server as any).getFileInfo.bind(server)
      
      // 创建不同类型的测试文件
      const jsonFile = path.join(tempDir, 'test.json')
      await fs.writeFile(jsonFile, '{"test": true}', 'utf-8')
      
      const uploadMethod = (server as any).uploadFile.bind(server)
      await uploadMethod(jsonFile)
      
      const uploadedFiles = await fs.readdir(uploadsDir)
      const jsonFileName = uploadedFiles.find(f => f.includes('json'))
      
      if (jsonFileName) {
        const info = await getInfoMethod(jsonFileName)
        expect(info.mimeType).toBe('application/json')
      }
    })
  })

  describe('error handling', () => {
    it('should handle non-existent source file', async () => {
      const uploadMethod = (server as any).uploadFile.bind(server)
      
      await expect(uploadMethod('/non/existent/file.txt')).rejects.toThrow()
    })

    it('should handle directory as source file', async () => {
      const uploadMethod = (server as any).uploadFile.bind(server)
      
      await expect(uploadMethod(tempDir)).rejects.toThrow('Path is not a file')
    })
  })
})
