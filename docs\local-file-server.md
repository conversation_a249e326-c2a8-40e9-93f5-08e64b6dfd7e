# 本地文件服务器 MCP

本地文件服务器是一个 MCP (Model Context Protocol) 服务器，提供文件上传功能，将文件复制到本地存储并返回可访问的文件URL。

## 功能特性

- **文件上传**: 将文件复制到本地上传目录
- **批量上传**: 支持一次上传多个文件
- **文件管理**: 列出、删除已上传的文件
- **文件信息**: 获取文件的详细信息（大小、修改时间、MIME类型等）
- **安全性**: 防止路径遍历攻击，文件只能在指定的上传目录内操作
- **唯一命名**: 自动生成唯一文件名避免冲突

## 配置

### 服务器配置

```typescript
// 默认配置
const server = new LocalFileServer()

// 自定义上传目录和基础URL
const server = new LocalFileServer(
  '/path/to/uploads',  // 上传目录
  'http://localhost:8080/files/'  // 基础URL
)
```

### MCP 配置

在 MCP 配置中添加本地文件服务器：

```json
{
  "mcpServers": {
    "localFileServer": {
      "command": "deepchat-inmemory/local-file-server",
      "args": ["/path/to/uploads", "file://"],
      "type": "inmemory",
      "descriptions": "本地文件上传服务器",
      "icons": "📁"
    }
  }
}
```

## 可用工具

### 1. upload_file

上传单个文件到本地服务器。

**参数:**
- `filePath` (string): 要上传的文件路径
- `targetDirectory` (string, 可选): 目标目录，相对于上传根目录

**示例:**
```json
{
  "name": "upload_file",
  "arguments": {
    "filePath": "/home/<USER>/document.pdf",
    "targetDirectory": "documents"
  }
}
```

**返回:**
```
File uploaded successfully!
Original: /home/<USER>/document.pdf
URL: file://documents/document_1234567890_abc12345.pdf
```

### 2. upload_multiple_files

批量上传多个文件。

**参数:**
- `filePaths` (string[]): 要上传的文件路径数组
- `targetDirectory` (string, 可选): 目标目录

**示例:**
```json
{
  "name": "upload_multiple_files",
  "arguments": {
    "filePaths": [
      "/home/<USER>/image1.jpg",
      "/home/<USER>/image2.png"
    ],
    "targetDirectory": "images"
  }
}
```

**返回:**
```
Upload Results:
✓ /home/<USER>/image1.jpg -> file://images/image1_1234567890_abc12345.jpg
✓ /home/<USER>/image2.png -> file://images/image2_1234567890_def67890.png
```

### 3. list_uploaded_files

列出已上传的文件。

**参数:**
- `directory` (string, 可选): 要列出的目录，相对于上传根目录

**示例:**
```json
{
  "name": "list_uploaded_files",
  "arguments": {
    "directory": "images"
  }
}
```

**返回:**
```
Uploaded Files (2):
image1_1234567890_abc12345.jpg (245.67 KB, 2024-01-15T10:30:00.000Z) - file://images/image1_1234567890_abc12345.jpg
image2_1234567890_def67890.png (189.23 KB, 2024-01-15T10:31:00.000Z) - file://images/image2_1234567890_def67890.png
```

### 4. delete_uploaded_file

删除已上传的文件。

**参数:**
- `fileName` (string): 要删除的文件名

**示例:**
```json
{
  "name": "delete_uploaded_file",
  "arguments": {
    "fileName": "document_1234567890_abc12345.pdf"
  }
}
```

**返回:**
```
File deleted successfully: document_1234567890_abc12345.pdf
```

### 5. get_file_info

获取文件的详细信息。

**参数:**
- `fileName` (string): 文件名

**示例:**
```json
{
  "name": "get_file_info",
  "arguments": {
    "fileName": "document_1234567890_abc12345.pdf"
  }
}
```

**返回:**
```
Name: document_1234567890_abc12345.pdf
Size: 1024.50 KB
Modified: 2024-01-15T10:30:00.000Z
MIME Type: application/pdf
URL: file://document_1234567890_abc12345.pdf
```

## 文件命名规则

为了避免文件名冲突，系统会自动生成唯一的文件名：

```
原文件名: document.pdf
生成文件名: document_1234567890_abc12345.pdf
```

命名格式: `{原始名称}_{时间戳}_{哈希值}.{扩展名}`

## 安全特性

1. **路径验证**: 所有文件操作都限制在指定的上传目录内
2. **路径遍历防护**: 防止 `../` 等路径遍历攻击
3. **文件类型检测**: 基于文件扩展名进行基本的MIME类型检测
4. **权限控制**: 只能操作上传目录内的文件

## 支持的文件类型

系统支持所有文件类型的上传，并为常见类型提供MIME类型检测：

- **图片**: .jpg, .jpeg, .png, .gif
- **文档**: .pdf, .txt, .json, .xml
- **压缩**: .zip
- **其他**: application/octet-stream (默认)

## 使用场景

1. **文档管理**: 上传和管理各种文档文件
2. **图片处理**: 上传图片文件供后续处理
3. **文件分享**: 生成本地文件URL用于分享
4. **临时存储**: 为AI处理提供临时文件存储
5. **文件备份**: 创建重要文件的本地副本

## 错误处理

服务器会处理各种错误情况：

- 源文件不存在
- 权限不足
- 磁盘空间不足
- 路径遍历攻击
- 无效的文件名

所有错误都会返回详细的错误信息，帮助用户理解和解决问题。

## 性能考虑

- 大文件上传可能需要较长时间
- 建议定期清理不需要的上传文件
- 监控上传目录的磁盘使用情况
- 考虑设置文件大小限制（可在未来版本中添加）

## 扩展功能

未来可能添加的功能：

- 文件大小限制配置
- 文件类型白名单/黑名单
- 文件压缩和优化
- 文件版本管理
- 文件访问日志
- HTTP服务器集成用于Web访问
