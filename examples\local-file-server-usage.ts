/**
 * 本地文件服务器使用示例
 * 
 * 这个示例展示了如何在DeepChat中配置和使用本地文件服务器MCP
 */

import { LocalFileServer } from '../src/main/presenter/mcpPresenter/inMemoryServers/localFileServer'
import { InMemoryTransport } from '@modelcontextprotocol/sdk/inMemory.js'
import { Client } from '@modelcontextprotocol/sdk/client/index.js'
import path from 'path'
import os from 'os'

async function demonstrateLocalFileServer() {
  console.log('=== 本地文件服务器演示 ===\n')

  // 1. 创建服务器实例
  const uploadsDir = path.join(os.tmpdir(), 'deepchat-uploads')
  const baseUrl = 'file://'
  
  const server = new LocalFileServer(uploadsDir, baseUrl)
  await server.initialize()
  
  console.log(`✓ 服务器初始化完成`)
  console.log(`  上传目录: ${uploadsDir}`)
  console.log(`  基础URL: ${baseUrl}\n`)

  // 2. 设置传输和客户端
  const transport = new InMemoryTransport()
  server.startServer(transport.server)
  
  const client = new Client(
    { name: 'LocalFileServerDemo', version: '1.0.0' },
    { capabilities: { tools: {} } }
  )
  
  await client.connect(transport.client)
  console.log('✓ 客户端连接成功\n')

  try {
    // 3. 获取可用工具列表
    console.log('--- 获取可用工具 ---')
    const tools = await client.listTools()
    console.log('可用工具:')
    tools.tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`)
    })
    console.log()

    // 4. 创建测试文件
    console.log('--- 创建测试文件 ---')
    const testFile = path.join(os.tmpdir(), 'test-document.txt')
    const fs = await import('fs/promises')
    await fs.writeFile(testFile, 'This is a test document for upload demonstration.', 'utf-8')
    console.log(`✓ 测试文件创建: ${testFile}\n`)

    // 5. 上传单个文件
    console.log('--- 上传单个文件 ---')
    const uploadResult = await client.callTool({
      name: 'upload_file',
      arguments: {
        filePath: testFile,
        targetDirectory: 'documents'
      }
    })
    console.log('上传结果:')
    console.log(uploadResult.content[0].text)
    console.log()

    // 6. 列出已上传的文件
    console.log('--- 列出已上传的文件 ---')
    const listResult = await client.callTool({
      name: 'list_uploaded_files',
      arguments: {}
    })
    console.log('文件列表:')
    console.log(listResult.content[0].text)
    console.log()

    // 7. 创建更多测试文件进行批量上传
    console.log('--- 创建更多测试文件 ---')
    const testFiles = []
    for (let i = 1; i <= 3; i++) {
      const file = path.join(os.tmpdir(), `test-image-${i}.txt`)
      await fs.writeFile(file, `This is test image ${i} content.`, 'utf-8')
      testFiles.push(file)
      console.log(`✓ 创建: ${file}`)
    }
    console.log()

    // 8. 批量上传文件
    console.log('--- 批量上传文件 ---')
    const batchUploadResult = await client.callTool({
      name: 'upload_multiple_files',
      arguments: {
        filePaths: testFiles,
        targetDirectory: 'images'
      }
    })
    console.log('批量上传结果:')
    console.log(batchUploadResult.content[0].text)
    console.log()

    // 9. 再次列出所有文件
    console.log('--- 列出所有已上传的文件 ---')
    const allFilesResult = await client.callTool({
      name: 'list_uploaded_files',
      arguments: {}
    })
    console.log('所有文件:')
    console.log(allFilesResult.content[0].text)
    console.log()

    // 10. 获取特定文件信息
    console.log('--- 获取文件详细信息 ---')
    // 从列表结果中提取第一个文件名
    const fileListText = allFilesResult.content[0].text as string
    const firstFileName = fileListText.split('\n')[1]?.split(' ')[0]
    
    if (firstFileName) {
      const fileInfoResult = await client.callTool({
        name: 'get_file_info',
        arguments: {
          fileName: firstFileName
        }
      })
      console.log(`文件信息 (${firstFileName}):`)
      console.log(fileInfoResult.content[0].text)
      console.log()

      // 11. 删除文件
      console.log('--- 删除文件 ---')
      const deleteResult = await client.callTool({
        name: 'delete_uploaded_file',
        arguments: {
          fileName: firstFileName
        }
      })
      console.log('删除结果:')
      console.log(deleteResult.content[0].text)
      console.log()
    }

    // 12. 最终文件列表
    console.log('--- 最终文件列表 ---')
    const finalListResult = await client.callTool({
      name: 'list_uploaded_files',
      arguments: {}
    })
    console.log('剩余文件:')
    console.log(finalListResult.content[0].text)

  } catch (error) {
    console.error('演示过程中发生错误:', error)
  } finally {
    // 清理
    await client.close()
    console.log('\n✓ 演示完成，客户端已关闭')
  }
}

// MCP 配置示例
export const mcpConfigExample = {
  "mcpServers": {
    "localFileServer": {
      "command": "deepchat-inmemory/local-file-server",
      "args": [
        "/path/to/your/uploads/directory",  // 自定义上传目录
        "file://"                           // 基础URL
      ],
      "type": "inmemory",
      "descriptions": "本地文件上传和管理服务器",
      "icons": "📁",
      "autoApprove": [
        "upload_file",
        "list_uploaded_files",
        "get_file_info"
      ]
    }
  },
  "defaultServers": ["localFileServer"],
  "mcpEnabled": true
}

// 在实际应用中的使用场景
export const useCases = {
  "文档管理": {
    description: "上传和管理各种文档文件",
    example: {
      tool: "upload_file",
      arguments: {
        filePath: "/home/<USER>/important-document.pdf",
        targetDirectory: "documents/important"
      }
    }
  },
  
  "图片处理": {
    description: "上传图片文件供AI分析",
    example: {
      tool: "upload_multiple_files",
      arguments: {
        filePaths: [
          "/home/<USER>/photo1.jpg",
          "/home/<USER>/photo2.png"
        ],
        targetDirectory: "images/analysis"
      }
    }
  },
  
  "临时文件存储": {
    description: "为AI处理创建临时文件副本",
    example: {
      tool: "upload_file",
      arguments: {
        filePath: "/tmp/generated-report.json",
        targetDirectory: "temp"
      }
    }
  },
  
  "文件备份": {
    description: "创建重要文件的本地备份",
    example: {
      tool: "upload_file",
      arguments: {
        filePath: "/home/<USER>/config.json",
        targetDirectory: "backups"
      }
    }
  }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  demonstrateLocalFileServer().catch(console.error)
}
